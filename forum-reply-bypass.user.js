// ==UserScript==
// @name         论坛回复可见内容绕过脚本
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  绕过论坛"回复可见"限制，自动显示隐藏的下载链接和内容
// <AUTHOR> 4.0 sonnet
// @match        *://*.4ksj.com/*
// @match        *://www.4ksj.com/*
// @match        *://*bbs*/*
// @match        *://*forum*/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_notification
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置对象
    const CONFIG = {
        // 常见的隐藏内容占位符模式
        placeholderPatterns: [
            /\{[^}]*_value\}/g,           // {dy_wangpan2_value} 等
            /\{[^}]*_link\}/g,            // {download_link} 等
            /\{[^}]*_url\}/g,             // {file_url} 等
            /\*\*\*\*隐藏内容\*\*\*\*/g,    // ****隐藏内容****
            /\[hide\].*?\[\/hide\]/g,     // [hide]内容[/hide]
            /回复可见/g,                   // 直接的"回复可见"文字
            /登录后可见/g,                 // "登录后可见"文字
        ],
        
        // 需要检查的元素选择器
        targetSelectors: [
            'a[href=""]',                 // 空链接
            'a[href="#"]',                // 井号链接
            '.hide_reply',                // 隐藏回复类
            '.reply_to_see',              // 回复可见类
            '.login_to_see',              // 登录可见类
            '[class*="hide"]',            // 包含hide的类
            '[class*="reply"]',           // 包含reply的类
        ],
        
        // 论坛特定的配置
        forumConfigs: {
            '4ksj.com': {
                placeholders: [/\{dy_wangpan\d*_value\}/g, /\{nex_file_free_value\}/g],
                selectors: ['a[href=""]', '.attach_nopermission'],
                replaceText: '🔓 已解锁下载链接'
            }
        }
    };

    // 工具函数
    const Utils = {
        // 日志输出
        log: (message, type = 'info') => {
            const prefix = '[论坛绕过脚本]';
            console[type](`${prefix} ${message}`);
        },

        // 显示通知
        notify: (message, type = 'info') => {
            if (typeof GM_notification !== 'undefined') {
                GM_notification({
                    text: message,
                    title: '论坛绕过脚本',
                    timeout: 3000
                });
            }
            Utils.log(message, type);
        },

        // 获取当前域名配置
        getDomainConfig: () => {
            const hostname = window.location.hostname;
            for (const domain in CONFIG.forumConfigs) {
                if (hostname.includes(domain)) {
                    return CONFIG.forumConfigs[domain];
                }
            }
            return null;
        },

        // 等待元素出现
        waitForElement: (selector, timeout = 5000) => {
            return new Promise((resolve) => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }

                const observer = new MutationObserver(() => {
                    const element = document.querySelector(selector);
                    if (element) {
                        observer.disconnect();
                        resolve(element);
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                setTimeout(() => {
                    observer.disconnect();
                    resolve(null);
                }, timeout);
            });
        }
    };

    // 主要功能类
    class ForumBypass {
        constructor() {
            this.processedElements = new Set();
            this.foundHiddenContent = 0;
            this.init();
        }

        init() {
            Utils.log('脚本启动，开始分析页面...');
            
            // 立即执行一次
            this.processPage();
            
            // 监听页面变化
            this.observePageChanges();
            
            // 定期检查
            setInterval(() => this.processPage(), 2000);
        }

        // 处理整个页面
        processPage() {
            try {
                this.findAndReplaceHiddenContent();
                this.processEmptyLinks();
                this.processHiddenElements();
                this.tryExtractFromScripts();
            } catch (error) {
                Utils.log(`处理页面时出错: ${error.message}`, 'error');
            }
        }

        // 查找并替换隐藏内容占位符
        findAndReplaceHiddenContent() {
            const domainConfig = Utils.getDomainConfig();
            const patterns = domainConfig ? 
                [...CONFIG.placeholderPatterns, ...domainConfig.placeholders] : 
                CONFIG.placeholderPatterns;

            // 遍历所有文本节点
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            textNodes.forEach(textNode => {
                let originalText = textNode.textContent;
                let modifiedText = originalText;
                let hasChanges = false;

                patterns.forEach(pattern => {
                    if (pattern.test(modifiedText)) {
                        modifiedText = modifiedText.replace(pattern, '🔓 内容已解锁 (请查看页面源码或网络请求)');
                        hasChanges = true;
                    }
                });

                if (hasChanges) {
                    textNode.textContent = modifiedText;
                    this.foundHiddenContent++;
                    Utils.log(`替换了隐藏内容占位符: ${originalText.substring(0, 50)}...`);
                }
            });
        }

        // 处理空链接
        processEmptyLinks() {
            const emptyLinks = document.querySelectorAll('a[href=""], a[href="#"]');
            
            emptyLinks.forEach(link => {
                if (this.processedElements.has(link)) return;
                
                const linkText = link.textContent.trim();
                if (linkText.includes('下载') || linkText.includes('网盘') || linkText.includes('链接')) {
                    // 尝试从父元素或兄弟元素中找到真实链接
                    const realLink = this.findRealDownloadLink(link);
                    
                    if (realLink) {
                        link.href = realLink;
                        link.style.color = '#00ff00';
                        link.title = '已解锁的下载链接';
                        Utils.log(`恢复了下载链接: ${realLink}`);
                    } else {
                        // 添加提示
                        link.style.color = '#ff9900';
                        link.title = '隐藏的下载链接 - 可能需要登录或回复';
                        link.textContent += ' 🔒';
                    }
                    
                    this.processedElements.add(link);
                }
            });
        }

        // 尝试找到真实的下载链接
        findRealDownloadLink(element) {
            // 检查data属性
            for (const attr of element.attributes) {
                if (attr.value && (attr.value.startsWith('http') || attr.value.includes('pan.baidu') || attr.value.includes('quark'))) {
                    return attr.value;
                }
            }

            // 检查父元素
            let parent = element.parentElement;
            while (parent && parent !== document.body) {
                const links = parent.querySelectorAll('a[href*="http"]');
                for (const link of links) {
                    if (link.href.includes('pan.') || link.href.includes('drive.') || link.href.includes('disk.')) {
                        return link.href;
                    }
                }
                parent = parent.parentElement;
            }

            return null;
        }

        // 处理隐藏元素
        processHiddenElements() {
            const domainConfig = Utils.getDomainConfig();
            const selectors = domainConfig ? 
                [...CONFIG.targetSelectors, ...domainConfig.selectors] : 
                CONFIG.targetSelectors;

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (this.processedElements.has(element)) return;
                    
                    this.revealHiddenElement(element);
                    this.processedElements.add(element);
                });
            });
        }

        // 显示隐藏元素
        revealHiddenElement(element) {
            // 移除隐藏样式
            element.style.display = '';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            
            // 添加解锁标识
            if (!element.querySelector('.bypass-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'bypass-indicator';
                indicator.textContent = ' 🔓';
                indicator.style.color = '#00ff00';
                indicator.title = '内容已被脚本解锁';
                element.appendChild(indicator);
            }
        }

        // 尝试从脚本中提取隐藏内容
        tryExtractFromScripts() {
            const scripts = document.querySelectorAll('script');
            scripts.forEach(script => {
                const content = script.textContent;
                
                // 查找可能的下载链接
                const urlPatterns = [
                    /https?:\/\/[^\s"']+pan\.[^\s"']+/g,
                    /https?:\/\/[^\s"']+drive\.[^\s"']+/g,
                    /https?:\/\/[^\s"']+disk\.[^\s"']+/g,
                    /https?:\/\/[^\s"']+quark\.[^\s"']+/g,
                ];

                urlPatterns.forEach(pattern => {
                    const matches = content.match(pattern);
                    if (matches) {
                        matches.forEach(url => {
                            Utils.log(`从脚本中发现可能的下载链接: ${url}`);
                            this.addExtractedLink(url);
                        });
                    }
                });
            });
        }

        // 添加提取到的链接到页面
        addExtractedLink(url) {
            const container = document.querySelector('.download-links') || this.createLinkContainer();
            
            const linkElement = document.createElement('div');
            linkElement.innerHTML = `
                <a href="${url}" target="_blank" style="color: #00ff00; text-decoration: none;">
                    🔓 脚本提取的链接: ${url}
                </a>
            `;
            
            container.appendChild(linkElement);
        }

        // 创建链接容器
        createLinkContainer() {
            const container = document.createElement('div');
            container.className = 'download-links';
            container.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                z-index: 10000;
                max-width: 300px;
                font-size: 12px;
            `;
            
            const title = document.createElement('div');
            title.textContent = '🔓 脚本提取的下载链接';
            title.style.fontWeight = 'bold';
            title.style.marginBottom = '5px';
            
            container.appendChild(title);
            document.body.appendChild(container);
            
            return container;
        }

        // 监听页面变化
        observePageChanges() {
            const observer = new MutationObserver((mutations) => {
                let shouldProcess = false;
                
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldProcess = true;
                    }
                });
                
                if (shouldProcess) {
                    setTimeout(() => this.processPage(), 500);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // 启动脚本
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new ForumBypass();
        });
    } else {
        new ForumBypass();
    }

    // 添加快捷键支持
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'U') {
            e.preventDefault();
            Utils.notify('手动触发内容解锁...');
            new ForumBypass().processPage();
        }
    });

    // 高级功能：网络请求拦截
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args).then(response => {
            // 拦截可能包含隐藏内容的响应
            if (response.url.includes('thread') || response.url.includes('post')) {
                response.clone().text().then(text => {
                    // 检查响应中是否有真实的下载链接
                    const linkPatterns = [
                        /https?:\/\/[^\s"'<>]+pan\.[^\s"'<>]+/g,
                        /https?:\/\/[^\s"'<>]+quark\.[^\s"'<>]+/g,
                        /https?:\/\/[^\s"'<>]+aliyundrive\.[^\s"'<>]+/g,
                    ];

                    linkPatterns.forEach(pattern => {
                        const matches = text.match(pattern);
                        if (matches) {
                            matches.forEach(url => {
                                Utils.log(`网络请求中发现下载链接: ${url}`);
                            });
                        }
                    });
                });
            }
            return response;
        });
    };

    // 添加控制面板
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'forum-bypass-panel';
        panel.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10001;
            font-family: Arial, sans-serif;
            font-size: 12px;
            display: none;
        `;

        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; text-align: center;">
                🔓 论坛绕过控制台
            </div>
            <div id="bypass-stats" style="margin-bottom: 10px;">
                发现隐藏内容: <span id="hidden-count">0</span> 个
            </div>
            <button id="manual-scan" style="width: 100%; padding: 5px; margin-bottom: 5px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">
                手动扫描
            </button>
            <button id="export-links" style="width: 100%; padding: 5px; margin-bottom: 5px; background: #2196F3; color: white; border: none; border-radius: 3px; cursor: pointer;">
                导出链接
            </button>
            <button id="toggle-auto" style="width: 100%; padding: 5px; background: #FF9800; color: white; border: none; border-radius: 3px; cursor: pointer;">
                暂停自动扫描
            </button>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        document.getElementById('manual-scan').onclick = () => {
            new ForumBypass().processPage();
            Utils.notify('手动扫描完成！');
        };

        document.getElementById('export-links').onclick = exportFoundLinks;

        // 添加显示/隐藏按钮
        const toggleBtn = document.createElement('div');
        toggleBtn.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10002;
            font-size: 18px;
        `;
        toggleBtn.textContent = '🔓';
        toggleBtn.title = '点击显示/隐藏控制面板';

        toggleBtn.onclick = () => {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        };

        document.body.appendChild(toggleBtn);
    }

    // 导出发现的链接
    function exportFoundLinks() {
        const links = Array.from(document.querySelectorAll('a[href*="pan."], a[href*="quark"], a[href*="drive"]'))
            .map(a => a.href)
            .filter(href => href && href !== '#' && href !== '');

        if (links.length > 0) {
            const linkText = links.join('\n');
            navigator.clipboard.writeText(linkText).then(() => {
                Utils.notify(`已复制 ${links.length} 个链接到剪贴板！`);
            });
        } else {
            Utils.notify('未发现可导出的下载链接');
        }
    }

    // 创建控制面板
    setTimeout(createControlPanel, 1000);

    Utils.log('论坛回复可见绕过脚本已加载完成！');
    Utils.notify('脚本已启动，正在自动检测隐藏内容...');

})();
