# 论坛回复可见内容绕过脚本 使用说明

## 🎯 脚本功能

这个油猴脚本专门用于绕过论坛中的"回复可见"限制，自动显示隐藏的下载链接和内容。

### 主要特性

1. **自动检测隐藏内容** - 识别常见的占位符模式如 `{dy_wangpan2_value}`
2. **智能链接恢复** - 尝试从页面源码中恢复真实的下载链接
3. **网络请求拦截** - 监控AJAX请求，提取可能的隐藏内容
4. **可视化控制面板** - 提供友好的操作界面
5. **多论坛支持** - 支持4K世界等多个论坛平台

## 📦 安装步骤

### 1. 安装油猴扩展

首先需要在浏览器中安装油猴(Tampermonkey)扩展：

- **Chrome**: 访问 [Chrome Web Store](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: 访问 [Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
- **Edge**: 访问 [Microsoft Store](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

### 2. 安装脚本

1. 点击油猴扩展图标
2. 选择"添加新脚本"
3. 删除默认内容，复制粘贴 `forum-reply-bypass.user.js` 中的全部代码
4. 按 `Ctrl+S` 保存脚本

### 3. 启用脚本

确保脚本在油猴管理面板中处于启用状态。

## 🚀 使用方法

### 自动模式

脚本安装后会自动运行：

1. 访问支持的论坛网站（如 4ksj.com）
2. 脚本会自动检测页面中的隐藏内容
3. 隐藏的占位符会被替换为"🔓 内容已解锁"提示
4. 空的下载链接会被标记或恢复

### 控制面板

点击页面右上角的 🔓 图标打开控制面板：

- **手动扫描**: 立即重新扫描页面
- **导出链接**: 将发现的下载链接复制到剪贴板
- **暂停自动扫描**: 临时停止自动检测

### 快捷键

- `Ctrl+Shift+U`: 手动触发内容解锁

## 🎯 支持的论坛

### 已测试论坛

- **4K世界** (4ksj.com) - 完全支持
- **其他Discuz论坛** - 基础支持

### 支持的隐藏内容类型

1. **占位符模式**:
   - `{dy_wangpan2_value}`
   - `{nex_file_free_value}`
   - `{download_link}`
   - `****隐藏内容****`
   - `[hide]内容[/hide]`

2. **下载链接**:
   - 百度网盘 (pan.baidu.com)
   - 夸克网盘 (quark.cn)
   - 阿里云盘 (aliyundrive.com)
   - Google Drive
   - 其他网盘服务

## ⚙️ 高级配置

### 自定义论坛支持

可以在脚本中的 `CONFIG.forumConfigs` 部分添加新的论坛配置：

```javascript
'your-forum.com': {
    placeholders: [/\{custom_pattern\}/g],
    selectors: ['.custom-hidden-class'],
    replaceText: '🔓 已解锁内容'
}
```

### 修改检测模式

在 `CONFIG.placeholderPatterns` 中添加新的正则表达式模式。

## 🔧 故障排除

### 常见问题

1. **脚本不工作**
   - 检查油猴扩展是否启用
   - 确认脚本在管理面板中处于启用状态
   - 刷新页面重试

2. **部分内容未解锁**
   - 使用手动扫描功能
   - 检查浏览器控制台是否有错误信息
   - 某些论坛可能使用服务器端验证，无法绕过

3. **控制面板不显示**
   - 检查是否有其他扩展冲突
   - 尝试禁用广告拦截器
   - 清除浏览器缓存

### 调试模式

打开浏览器开发者工具(F12)，在控制台中查看脚本日志：

```
[论坛绕过脚本] 脚本启动，开始分析页面...
[论坛绕过脚本] 替换了隐藏内容占位符: {dy_wangpan2_value}
[论坛绕过脚本] 恢复了下载链接: https://pan.baidu.com/...
```

## 📋 更新日志

### v1.0.0 (2025-07-09)
- 初始版本发布
- 支持4K世界论坛
- 基础的占位符替换功能
- 可视化控制面板
- 网络请求拦截功能

## ⚠️ 免责声明

1. 本脚本仅供学习和研究使用
2. 请遵守论坛的使用条款和规则
3. 下载的内容请注意版权问题
4. 作者不承担任何法律责任

## 🤝 贡献

如果你发现bug或有改进建议，欢迎：

1. 在控制台查看错误信息
2. 提供具体的论坛URL和问题描述
3. 建议新的功能或改进

## 📞 技术支持

如需技术支持，请提供以下信息：

- 浏览器版本
- 油猴版本
- 论坛网址
- 具体的错误信息
- 控制台日志

---

**注意**: 使用本脚本时请遵守相关法律法规和论坛规则，理性使用，避免对论坛服务器造成过大负担。
